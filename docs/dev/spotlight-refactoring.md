# Spotlight System Refactoring

**Date**: 2025-01-17  
**Issue**: Massive useEffect Hook with Complex Logic (290 lines)  
**Solution**: React Context API with Custom Hooks Pattern

## Overview

This document describes the refactoring of the massive 290-line useEffect hook in `app/page.tsx` into a modular, maintainable system using React Context API and custom hooks.

## Problem Statement

The original implementation had a single useEffect hook that handled:
- Mouse position tracking
- Spotlight animation logic
- Complex grid-based layout calculations
- Resize observation and event handling
- DOM manipulation
- State management

This violated the single responsibility principle and made the code extremely difficult to:
- Test individual components
- Debug issues
- Maintain and extend
- Understand the flow of logic

## Solution Architecture

### 1. Context-Based State Management

**File**: `contexts/spotlight-context.tsx`

Created a React Context provider that manages:
- Global spotlight state (mouse position, hover state, initialization)
- Shared refs for DOM elements
- State dispatch actions using useReducer pattern

**Benefits**:
- Centralized state management
- Eliminates prop drilling
- Type-safe state updates
- Shared refs across multiple hooks

### 2. Custom Hooks for Separation of Concerns

#### `useSpotlight()` - `hooks/use-spotlight.ts`
- **Responsibility**: Spotlight animation logic
- **Features**: 
  - Opacity calculations using smoothstep function
  - Spotlight positioning
  - Animation frame management
  - Center computation for network problem elements

#### `useMouseTracking()` - `hooks/use-mouse-tracking.ts`
- **Responsibility**: Mouse event handling
- **Features**:
  - Pointer and mouse event listeners
  - Hover state detection
  - Proper event cleanup
  - Performance optimization with passive listeners

#### `useLayoutCalculation()` - `hooks/use-layout-calculation.ts`
- **Responsibility**: Complex grid-based positioning
- **Features**:
  - Poisson-like grid placement algorithm
  - Collision detection with hero elements
  - Randomized positioning with constraints
  - Fallback placement strategies

#### `useResizeObserver()` - `hooks/use-resize-observer.ts`
- **Responsibility**: Layout change detection
- **Features**:
  - ResizeObserver integration
  - Window resize and scroll handling
  - Initialization timing management
  - Proper cleanup of observers

#### `useTypingAnimation()` - `hooks/use-typing-animation.ts`
- **Responsibility**: Terminal typing effect
- **Features**:
  - Character-by-character typing
  - Looping animation
  - Configurable timing
  - Proper dependency management

### 3. Constants Extraction

**File**: `lib/spotlight-constants.ts`

Extracted all magic numbers into named constants:
- `SPOTLIGHT_RADIUS = 120`
- `SPOTLIGHT_INNER_RADIUS = 60`
- `SPOTLIGHT_OUTER_RADIUS = 140`
- `TYPING_CHAR_DELAY = 22`
- `TYPING_LINE_DELAY = 420`
- And more...

## Implementation Details

### Component Structure

```tsx
// Main component with provider
export default function HomePage() {
  return (
    <SpotlightProvider>
      <HomePageContent />
    </SpotlightProvider>
  )
}

// Inner component using hooks
function HomePageContent() {
  const { refs } = useSpotlightContext()
  
  useMouseTracking()
  useResizeObserver()
  
  const { typedLines, activeLine } = useTypingAnimation(terminalLines)
  
  return (
    // JSX using refs from context
  )
}
```

### State Management Flow

1. **SpotlightProvider** creates shared state and refs
2. **Custom hooks** subscribe to context and manage specific concerns
3. **Components** use hooks declaratively without implementation details
4. **State updates** flow through dispatch actions

### Performance Optimizations

- **Passive event listeners** for better scroll performance
- **RequestAnimationFrame** for smooth animations
- **Memoized calculations** in custom hooks
- **Proper cleanup** of all event listeners and observers

## Benefits Achieved

### 1. Maintainability
- Each hook has a single, clear responsibility
- Easy to locate and fix specific functionality
- Modular code that can be tested independently

### 2. Reusability
- Custom hooks can be reused in other components
- Context pattern scales to multiple components
- Constants can be shared across the application

### 3. Testability
- Each hook can be unit tested in isolation
- State management is predictable and testable
- Mocked dependencies are easier to implement

### 4. Performance
- No unnecessary re-renders due to proper state separation
- Optimized event handling with passive listeners
- Efficient animation frame management

### 5. Type Safety
- Full TypeScript support throughout
- Type-safe state updates and refs
- Compile-time error detection

## Migration Impact

### Before (Issues)
- 290-line useEffect hook
- Mixed responsibilities
- Difficult to debug
- Hard to test
- Performance concerns

### After (Improvements)
- 6 focused custom hooks
- Clear separation of concerns
- Easy to debug individual features
- Unit testable components
- Optimized performance

## Files Created/Modified

### New Files
- `contexts/spotlight-context.tsx` - Context provider and types
- `hooks/use-spotlight.ts` - Spotlight animation logic
- `hooks/use-mouse-tracking.ts` - Mouse event handling
- `hooks/use-layout-calculation.ts` - Grid placement algorithm
- `hooks/use-resize-observer.ts` - Resize and layout observation
- `hooks/use-typing-animation.ts` - Terminal typing effect
- `lib/spotlight-constants.ts` - Extracted constants

### Modified Files
- `app/page.tsx` - Refactored to use new hooks and context

## Testing Recommendations

1. **Unit Tests** for each custom hook
2. **Integration Tests** for context provider
3. **Visual Regression Tests** for spotlight animation
4. **Performance Tests** for mouse tracking efficiency

## Future Enhancements

1. **Error Boundaries** around spotlight system
2. **Performance Monitoring** for animation frames
3. **Accessibility Improvements** for keyboard navigation
4. **Configuration Options** for animation parameters

## Conclusion

The refactoring successfully transformed a monolithic 290-line useEffect into a modular, maintainable system. The new architecture follows React best practices, improves performance, and provides a solid foundation for future enhancements.

The spotlight system now demonstrates proper separation of concerns, making it easier to understand, test, and maintain while preserving all original functionality.
